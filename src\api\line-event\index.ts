import type { LineCurrentStatusResponse } from './types'
import { kyGet, kyPost } from '~/utils/request'

export const LineEventStatusApi = {
  toggleStatus: (param: LineCurrentStatusResponse) => kyPost('line-manual-events/toggleStatus', param),
  currentStatues: (lineCodes: string) => kyGet('line-manual-events/currentStatuses', { lineCodes }).json<LineCurrentStatusResponse[]>(),
  currentStatus: (lineCode: string) => kyGet(`line-manual-events/currentStatus/${lineCode}`).json<LineCurrentStatusResponse>(),
}
