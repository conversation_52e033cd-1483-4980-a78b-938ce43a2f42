/**
 * 线体状态
 */
export interface LineCurrentStatusResponse {
  lineCode: string
  status: 'running' | 'stopped'
  startTime: Date
}

/**
 * 线体响应状态
 */
export interface ToggleStatusResponse {
  lineCode: string
  newStatus: string
}

export interface OperatingRecordsResponse {
  lineCode: string
  records: OperatingRecordDto[]
}

export interface OperatingRecordDto {
  startTime: Date
  endTime: Date
}
