import type { PageList, Pageable } from '../common/type'
import type { TheoreticalSample } from './types'
import { kyPost } from '~/utils/request'

export const theoreticalSampleApi = {
  page: (param: Pageable<Partial<TheoreticalSample>>) => kyPost('theoretical-output-sample/page', param).json<PageList<TheoreticalSample>>(),
  export: (param: Partial<TheoreticalSample>) => kyPost('theoretical-output-sample/export', param),
}
