<script setup lang="ts">
import { analyzeApi } from '~/api/analyze'
import type { OeeResult } from '~/api/analyze/type'
import { TriggerRecordApi } from '~/api/feedback/trigger'
import { LineEventStatusApi } from '~/api/line-event'
import { useAnalyticsSearchSchema } from '~/pages/dashboard/schema'

const props = defineProps<{
  code: string
  startTime: Date
  endTime: Date
}>()

interface LineStatus {
  anomaliesName: string
  triggerTime: Date | null
}

// 状态管理
const form = useAnalyticsSearchSchema()
const loading = ref<boolean>(false)

// 数据状态
const oee = ref<OeeResult>()
const lineStatus = ref<LineStatus>({
  anomaliesName: '正常',
  triggerTime: null,
})
const lineEventStatus = ref<'running' | 'stopped'>('stopped')

// API 调用封装
const loadData = form.handleSubmit(async (values) => {
  loading.value = true
  try {
    const res = await analyzeApi.getLineOee(values)
    oee.value = res.oeeResult
  }
  finally {
    loading.value = false
  }
})

const loadProductionLineStatus = form.handleSubmit(async (values) => {
  loading.value = true
  try {
    const res = await TriggerRecordApi.latestOpenException(values.code)
    if (res) {
      lineStatus.value = {
        anomaliesName: res.anomaliesName,
        triggerTime: res.triggerTime,
      }
    }
    else {
      lineStatus.value = {
        anomaliesName: '正常',
        triggerTime: null,
      }
    }
  }
  finally {
    loading.value = false
  }
})

const loadProductionLineEventStatus = form.handleSubmit(async (values) => {
  loading.value = true
  try {
    const res = await LineEventStatusApi.currentStatus(values.code)
    if (res) {
      lineEventStatus.value = res.status
    }
  }
  finally {
    loading.value = false
  }
})

// 生命周期和监听器
watch(props, (newProps) => {
  if (newProps) {
    // 设置产品线
    form.setFieldValue('code', newProps.code)
    form.setFieldValue('startTime', newProps.startTime)
    form.setFieldValue('endTime', newProps.endTime)
    // 加载数据
    loadData()
    // 加载线体状态
    loadProductionLineStatus()
    // 加载开线状态
    loadProductionLineEventStatus()
  }
}, { immediate: true, deep: true })
</script>

<template>
  <div class="line-container">
    <div class="line-dashboard-panel grid grid-cols-[1fr_9fr] box-border max-w-full w-full gap-4 p-4">
      <!-- 线体状态卡片 -->
      <div class="col-span-1 row-span-2 flex justify-start">
        <LineStatusCard
          :code="props.code"
          :anomalies-name="lineStatus.anomaliesName"
          :trigger-time="lineStatus.triggerTime"
          :event-status="lineEventStatus"
        />
      </div>

      <!-- 生产指标区域 -->
      <div class="col-span-1 row-span-1">
        <div class="grid grid-cols-8 box-border w-full gap-2">
          <MetricCard
            title="开班时间"
            :value="`${oee?.actualPlanTime ?? 0} h`"
            icon="pi pi-clock"
          />
          <MetricCard
            title="运机时间"
            :value="`${oee?.runTime ?? 0} h`"
            icon="pi pi-play"
          />
          <MetricCard
            title="停机时间"
            :value="`${oee?.stopTime ?? 0} h`"
            icon="pi pi-pause"
          />
          <MetricCard
            title="标准生产数量"
            :value="`${oee?.planBoard ?? 0}`"
            icon="pi pi-chart-bar"
          />
          <MetricCard
            title="实际生产数量"
            :value="`${oee?.actualBoard ?? 0}`"
            icon="pi pi-chart-line"
          />
          <MetricCard
            title="不良品数"
            :value="oee?.defectCount ?? 0"
            icon="pi pi-exclamation-triangle"
          />
          <MetricCard
            title="换线次数"
            :value="`${oee?.changeoverNum ?? 0}`"
            icon="pi pi-refresh"
          />
          <MetricCard
            title="换线时间"
            :value="`${oee?.changeoverTime ?? 0} min`"
            icon="pi pi-stopwatch"
          />
        </div>
      </div>

      <!-- OEE指标区域 -->
      <div class="col-span-1 row-span-1 border-t border-[rgba(59,130,246,0.2)] pt-4">
        <div class="grid grid-cols-4 box-border w-full gap-2">
          <OeeMetricCard
            title="运转率"
            :target-value="oee?.availabilityTarget ?? 0"
            :actual-value="oee?.availability ?? 0"
          />
          <OeeMetricCard
            title="有效生产率"
            :target-value="oee?.performanceTarget ?? 0"
            :actual-value="oee?.performance ?? 0"
          />
          <OeeMetricCard
            title="良品率"
            :target-value="oee?.qualityTarget ?? 0"
            :actual-value="oee?.quality ?? 0"
          />
          <OeeMetricCard
            title="OEE"
            :target-value="oee?.oeeTarget ?? 0"
            :actual-value="oee?.oee ?? 0"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.line-container {
  background: var(--line-container-bg);
  backdrop-filter: blur(12px);
  border: 1px solid rgba(59, 130, 246, 0.25);
  border-radius: 12px;
  transition: all 0.3s ease;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.25);
}

.line-dashboard-panel {
  width: 100%;
  max-width: 100%;
  box-sizing: border-box;
}

:global(.light) .border-t {
  border-color: rgba(59, 130, 246, 0.15);
}
</style>
