<script setup lang="ts">
import { useDateFormat } from '@vueuse/core'
import { useRouter } from 'vue-router'

const props = defineProps<{
  code: string
  anomaliesName: string
  triggerTime: Date | null
  eventStatus: 'running' | 'stopped'
}>()

const formattedTriggerTime = computed(() => {
  if (props.triggerTime) {
    return useDateFormat(props.triggerTime, 'MM/DD HH:mm').value
  }
  return ''
})

const statusClass = computed(() => {
  return props.anomaliesName === '正常' ? 'text-green-300' : 'text-red-300'
})

const statusIndicatorClass = computed(() => {
  return props.anomaliesName === '正常' ? 'status-normal' : 'status-error'
})

const buttonLabel = computed(() => {
  return props.eventStatus === 'running' ? '停线' : '开线'
})

const buttonSeverity = computed(() => {
  return props.eventStatus === 'running' ? 'danger' : 'success'
})

const buttonIcon = computed(() => {
  return props.eventStatus === 'running' ? 'pi pi-stop' : 'pi pi-play'
})

const router = useRouter()
</script>

<template>
  <div class="line-status-card">
    <!-- 线体编号 -->
    <div class="line-code-section" @click="router.push({ name: 'dashboard', params: { code: props.code } })">
      <div class="line-code-wrapper">
        <i class="pi line-icon pi-microchip" />
        <span class="text-3xl text-blue-400 font-bold">{{ code }}</span>
      </div>
      <div class="click-hint">
        <i class="pi pi-external-link" />
        <span>详情</span>
      </div>
    </div>

    <!-- 状态信息 -->
    <div class="status-info-section">
      <div class="status-item">
        <!-- <div class="status-label">
          状态
        </div> -->
        <div class="status-value" :class="statusClass">
          <i class="status-indicator" :class="statusIndicatorClass" />
          {{ anomaliesName }}
        </div>
      </div>

      <div v-if="triggerTime" class="status-item">
        <div class="status-label">
          触发时间
        </div>
        <div class="status-value time-value">
          <i class="pi pi-clock" />
          {{ formattedTriggerTime }}
        </div>
      </div>
    </div>

    <!-- 控制按钮 -->
    <div class="control-section">
      <Button
        :label="buttonLabel"
        :severity="buttonSeverity"
        :icon="buttonIcon"
        class="control-button"
        size="small"
        rounded
      />
    </div>
  </div>
</template>

<style scoped>
.line-status-card {
  background: linear-gradient(135deg, rgba(30, 41, 59, 0.9) 0%, rgba(15, 23, 42, 0.9) 100%);
  backdrop-filter: blur(16px);
  border: 1px solid rgba(59, 130, 246, 0.3);
  border-radius: 10px;
  padding: 1rem;
  min-height: 160px;
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  width: 100%;
  max-width: 100%;
  box-sizing: border-box;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.2);
}

.line-status-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: linear-gradient(
    90deg,
    rgba(59, 130, 246, 0.7) 0%,
    rgba(147, 51, 234, 0.7) 50%,
    rgba(59, 130, 246, 0.7) 100%
  );
}

.line-status-card:hover {
  transform: translateY(-4px);
  border-color: rgba(59, 130, 246, 0.4);
  box-shadow:
    0 16px 30px -5px rgba(0, 0, 0, 0.25),
    0 0 15px rgba(59, 130, 246, 0.15);
}

/* 亮色模式 */
:global(.light) .line-status-card {
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  border-color: rgba(59, 130, 246, 0.25);
  box-shadow: 0 6px 16px -4px rgba(0, 0, 0, 0.1);
}

:global(.light) .line-status-card:hover {
  box-shadow:
    0 12px 25px -5px rgba(0, 0, 0, 0.12),
    0 0 15px rgba(59, 130, 246, 0.1);
}

/* 线体编号区域 */
.line-code-section {
  cursor: pointer;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem;
  border-radius: 8px;
  background: rgba(59, 130, 246, 0.1);
  transition: all 0.2s ease;
  flex: 1;
}

.line-code-section:hover {
  background: rgba(59, 130, 246, 0.15);
  transform: scale(1.02);
}

.line-code-wrapper {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
}

.line-icon {
  font-size: 1.75rem;
  color: var(--text-secondary);
  filter: drop-shadow(0 2px 6px rgba(0, 0, 0, 0.3));
}

:global(.light) .line-icon {
  color: var(--text-secondary);
  filter: drop-shadow(0 1px 3px rgba(0, 0, 0, 0.1));
}

/* 线体编号文字样式 */
.line-code-wrapper .text-3xl {
  color: var(--text-primary) !important;
  text-shadow: 0 2px 6px rgba(0, 0, 0, 0.4);
}

:global(.light) .line-code-wrapper .text-3xl {
  color: var(--text-primary) !important;
  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.click-hint {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  font-size: 0.875rem;
  color: var(--text-muted);
  opacity: 0;
  transition: opacity 0.2s ease;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

:global(.light) .click-hint {
  color: var(--text-muted);
  text-shadow: none;
}

.line-code-section:hover .click-hint {
  opacity: 1;
}

/* 状态信息区域 */
.status-info-section {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  flex: 1;
  justify-content: center;
}

.status-item {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.status-label {
  font-size: 0.875rem;
  color: var(--text-secondary);
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

:global(.light) .status-label {
  color: var(--text-secondary);
  text-shadow: none;
}

.status-value {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 1rem;
  font-weight: 600;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
  justify-content: center;
  align-content: center;
}

:global(.light) .status-value {
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.status-indicator {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  flex-shrink: 0;
}

.text-green-300 .status-indicator {
  background: #6ee7b7;
  box-shadow: 0 0 12px rgba(110, 231, 183, 0.6);
}

.text-red-300 .status-indicator {
  background: #fca5a5;
  box-shadow: 0 0 12px rgba(252, 165, 165, 0.6);
}

.time-value {
  color: var(--text-muted);
  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
}

:global(.light) .time-value {
  color: var(--text-muted);
  text-shadow: none;
}

/* 控制按钮区域 */
.control-section {
  display: flex;
  justify-content: center;
}

.control-button {
  min-width: 80px;
  font-weight: 600;
  transition: all 0.2s ease;
}

.control-button:hover {
  transform: scale(1.05);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .line-status-card {
    padding: 1rem;
    min-height: 160px;
  }

  .line-code {
    font-size: 1.625rem;
  }

  .line-icon {
    font-size: 1.5rem;
  }

  .status-label {
    font-size: 0.8rem;
  }

  .status-value {
    font-size: 0.9rem;
  }
}
</style>
