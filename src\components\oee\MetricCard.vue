<script setup lang="ts">
defineProps<{
  title: string
  value: string | number
  icon?: string
}>()
</script>

<template>
  <div class="metric-card">
    <div class="metric-header">
      <i v-if="icon" :class="icon" class="metric-icon" />
      <span class="metric-title">{{ title }}</span>
    </div>
    <div class="metric-value">
      {{ value }}
    </div>
    <div class="metric-glow" />
  </div>
</template>

<style scoped>
.metric-card {
  position: relative;
  background: linear-gradient(135deg, rgba(30, 41, 59, 0.9) 0%, rgba(15, 23, 42, 0.9) 100%);
  backdrop-filter: blur(16px);
  border: 1px solid rgba(59, 130, 246, 0.25);
  border-radius: 10px;
  padding: 0.75rem;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 0.375rem;
  min-height: 80px;
  transition: all 0.3s ease;
  overflow: hidden;
  width: 100%;
  max-width: 100%;
  box-sizing: border-box;
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2);
}

.metric-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent 0%, rgba(59, 130, 246, 0.5) 50%, transparent 100%);
}

.metric-card:hover {
  transform: translateY(-2px);
  border-color: rgba(59, 130, 246, 0.35);
  box-shadow:
    0 10px 25px -3px rgba(0, 0, 0, 0.2),
    0 0 15px rgba(59, 130, 246, 0.1);
}

.metric-card:hover .metric-glow {
  opacity: 0.8;
}

/* 亮色模式 */
:global(.light) .metric-card {
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  border-color: rgba(59, 130, 246, 0.2);
  box-shadow: 0 4px 12px -2px rgba(0, 0, 0, 0.08);
}

:global(.light) .metric-card:hover {
  box-shadow:
    0 8px 20px -3px rgba(0, 0, 0, 0.1),
    0 0 15px rgba(59, 130, 246, 0.1);
}

.metric-header {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.metric-icon {
  font-size: 1.125rem;
  color: var(--text-secondary);
  flex-shrink: 0;
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.3));
}

:global(.light) .metric-icon {
  color: var(--text-secondary);
  filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.1));
}

.metric-title {
  font-size: 0.875rem;
  color: var(--text-primary);
  font-weight: 600;
  text-align: center;
  line-height: 1.2;
  word-break: break-word;
  hyphens: auto;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.4);
}

:global(.light) .metric-title {
  color: var(--text-primary);
  text-shadow: none;
}

.metric-value {
  font-size: 1.375rem;
  font-weight: 700;
  color: #93c5fd;
  text-shadow: 0 2px 6px rgba(0, 0, 0, 0.4);
  text-align: center;
}

:global(.light) .metric-value {
  color: #2563eb;
  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.metric-glow {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 80%;
  height: 80%;
  background: radial-gradient(ellipse at center, rgba(59, 130, 246, 0.15) 0%, transparent 70%);
  transform: translate(-50%, -50%);
  opacity: 0;
  transition: opacity 0.3s ease;
  pointer-events: none;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .metric-card {
    padding: 0.75rem;
    min-height: 80px;
  }

  .metric-title {
    font-size: 0.8rem;
  }

  .metric-value {
    font-size: 1.25rem;
  }

  .metric-icon {
    font-size: 1rem;
  }
}

@media (max-width: 480px) {
  .metric-card {
    padding: 0.5rem;
    min-height: 70px;
  }

  .metric-title {
    font-size: 0.75rem;
  }

  .metric-value {
    font-size: 1.125rem;
  }
}
</style>
