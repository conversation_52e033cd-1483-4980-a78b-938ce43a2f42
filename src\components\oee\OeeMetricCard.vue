<script setup lang="ts">
const props = defineProps<{
  title: string
  targetValue: string | number
  actualValue: string | number
}>()

// 计算达成率和状态
const achievementRate = computed(() => {
  const target = Number(props.targetValue)
  const actual = Number(props.actualValue)
  if (target === 0)
    return 0
  return Math.round((actual / target) * 100)
})

const performanceStatus = computed(() => {
  const rate = achievementRate.value
  if (rate >= 100)
    return 'excellent'
  if (rate >= 90)
    return 'good'
  if (rate >= 80)
    return 'warning'
  return 'poor'
})

const statusColor = computed(() => {
  switch (performanceStatus.value) {
    case 'excellent': return '#10b981' // green
    case 'good': return '#3b82f6' // blue
    case 'warning': return '#f59e0b' // amber
    case 'poor': return '#ef4444' // red
    default: return '#6b7280' // gray
  }
})
</script>

<template>
  <div class="oee-metric-card" :class="`status-${performanceStatus}`">
    <div class="card-header">
      <span class="metric-title">{{ title }}</span>
      <!-- <div class="achievement-badge" :style="{ backgroundColor: statusColor }">
        {{ achievementRate }}%
      </div> -->
    </div>

    <div class="values-section">
      <div class="value-item">
        <span class="value-label">目标</span>
        <span class="value-number target">{{ targetValue }}%</span>
      </div>
      <div class="value-divider" />
      <div class="value-item">
        <span class="value-label">实际</span>
        <span class="value-number actual" :style="{ color: statusColor }">{{ actualValue }}%</span>
      </div>
    </div>

    <div class="progress-bar">
      <div
        class="progress-fill"
        :style="{
          width: `${Math.min(achievementRate, 100)}%`,
          backgroundColor: statusColor,
        }"
      />
    </div>

    <div class="metric-glow" :style="{ backgroundColor: statusColor }" />
  </div>
</template>

<style scoped>
.oee-metric-card {
  position: relative;
  background: linear-gradient(135deg, rgba(30, 41, 59, 0.9) 0%, rgba(15, 23, 42, 0.9) 100%);
  backdrop-filter: blur(16px);
  border: 1px solid;
  border-radius: 12px;
  padding: 1rem;
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  min-height: 80px;
  transition: all 0.3s ease;
  overflow: hidden;
  width: 100%;
  max-width: 100%;
  box-sizing: border-box;
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2);
}

.oee-metric-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: linear-gradient(90deg, transparent 0%, var(--status-color, #6b7280) 50%, transparent 100%);
  opacity: 0.7;
}

.oee-metric-card:hover {
  transform: translateY(-3px);
  border-color: var(--status-color, rgba(59, 130, 246, 0.4));
  box-shadow:
    0 12px 30px -5px rgba(0, 0, 0, 0.2),
    0 0 15px var(--status-color, rgba(59, 130, 246, 0.1));
}

.oee-metric-card:hover .metric-glow {
  opacity: 0.25;
}

/* 状态特定样式 */
.status-excellent {
  border-color: rgba(16, 185, 129, 0.4);
  --status-color: #10b981;
}

.status-good {
  border-color: rgba(59, 130, 246, 0.4);
  --status-color: #3b82f6;
}

.status-warning {
  border-color: rgba(245, 158, 11, 0.4);
  --status-color: #f59e0b;
}

.status-poor {
  border-color: rgba(239, 68, 68, 0.4);
  --status-color: #ef4444;
}

/* 亮色模式 */
:global(.light) .oee-metric-card {
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  box-shadow: 0 6px 16px -4px rgba(0, 0, 0, 0.08);
}

:global(.light) .oee-metric-card:hover {
  box-shadow:
    0 12px 25px -5px rgba(0, 0, 0, 0.1),
    0 0 15px var(--status-color, rgba(59, 130, 246, 0.1));
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 0.5rem;
}

.metric-title {
  font-size: 1rem;
  color: var(--text-primary);
  font-weight: 600;
  flex: 1;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.4);
}

:global(.light) .metric-title {
  color: var(--text-primary);
  text-shadow: none;
}

.achievement-badge {
  padding: 0.25rem 0.5rem;
  border-radius: 12px;
  font-size: 0.875rem;
  font-weight: 700;
  color: #ffffff;
  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
  min-width: 50px;
  text-align: center;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.values-section {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.value-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.25rem;
  flex: 1;
}

.value-label {
  font-size: 0.8rem;
  color: var(--text-secondary);
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

:global(.light) .value-label {
  color: var(--text-secondary);
  text-shadow: none;
}

.value-number {
  font-size: 1.125rem;
  font-weight: 700;
  text-align: center;
}

.target {
  color: var(--text-primary);
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

:global(.light) .target {
  color: var(--text-primary);
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.value-divider {
  width: 1px;
  height: 30px;
  background: rgba(59, 130, 246, 0.25);
  flex-shrink: 0;
}

:global(.light) .value-divider {
  background: rgba(59, 130, 246, 0.2);
}

.progress-bar {
  position: relative;
  height: 6px;
  background: rgba(30, 41, 59, 0.5);
  border-radius: 3px;
  overflow: hidden;
  border: 1px solid rgba(59, 130, 246, 0.15);
}

:global(.light) .progress-bar {
  background: rgba(226, 232, 240, 0.7);
  border-color: rgba(59, 130, 246, 0.1);
}

.progress-fill {
  height: 100%;
  border-radius: 3px;
  transition: width 0.8s ease;
  position: relative;
}

.progress-fill::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(90deg, transparent 0%, rgba(255, 255, 255, 0.25) 50%, transparent 100%);
  animation: shimmer 2s infinite;
}

.metric-glow {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 100%;
  height: 100%;
  border-radius: 50%;
  transform: translate(-50%, -50%);
  opacity: 0;
  transition: opacity 0.3s ease;
  pointer-events: none;
  filter: blur(25px);
}

@keyframes shimmer {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .oee-metric-card {
    padding: 1rem;
    min-height: 120px;
  }

  .metric-title {
    font-size: 0.9rem;
  }

  .value-number {
    font-size: 1.05rem;
  }

  .achievement-badge {
    font-size: 0.8rem;
    padding: 0.2rem 0.4rem;
  }
}

@media (max-width: 480px) {
  .oee-metric-card {
    padding: 0.75rem;
    min-height: 100px;
  }

  .values-section {
    gap: 0.5rem;
  }

  .metric-title {
    font-size: 0.85rem;
  }

  .value-number {
    font-size: 1rem;
  }
}
</style>
