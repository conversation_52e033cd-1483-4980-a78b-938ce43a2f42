<script setup lang="ts">
import { useRoute } from 'vue-router'
import { analyzeApi } from '~/api/analyze'
import { productLineApi } from '~/api/line'
import type { ProductLine } from '~/api/line/types'
import LineContainer from '~/components/oee/LineContainer.vue'
import { useAggregationStore } from '~/stores/aggregation'
import Honghong from '~/assets/honghong.gif'

const startTime = ref<Date>()
const endTime = ref<Date>()
const route = useRoute()
const loading = ref<boolean>(false)
const aggregationStore = useAggregationStore()
const workshopCode = ref<string>()
const refreshInterval = ref<ReturnType<typeof setInterval>>()
const el = useTemplateRef<HTMLElement>('el')
const { width, height } = useWindowSize()
const { style } = useDraggable(el, {
  initialValue: { x: width.value * 0.01, y: height.value * 0.75 },
})

// 数据状态
const lineList = ref<ProductLine[]>([])
const show = ref<boolean>(false)

// 加载数据
async function loadData(code: string) {
  loading.value = true
  try {
    const res = await productLineApi.listByWorkShopId(code)
    lineList.value = res
  }
  finally {
    loading.value = false
  }
}

// 日期处理方法
async function initializeDates() {
  try {
    const serverTime = await analyzeApi.getServerTime()
    const now = new Date(serverTime)
    const currentDay = new Date(now)
    const nextDay = new Date(now)
    nextDay.setDate(now.getDate() + 1)

    startTime.value = new Date(currentDay.setHours(8, 0, 0, 0))
    endTime.value = new Date(nextDay.setHours(8, 0, 0, 0))
  }
  catch (error) {
    console.error('Failed to get server time:', error)
    // 使用当前时间
    const now = new Date()
    const currentDay = new Date(now)
    const nextDay = new Date(now)
    nextDay.setDate(now.getDate() + 1)

    startTime.value = new Date(currentDay.setHours(8, 0, 0, 0))
    endTime.value = new Date(nextDay.setHours(8, 0, 0, 0))
  }
}

function checkAndUpdateDates() {
  const now = new Date()
  if (!endTime.value || now > endTime.value) {
    initializeDates()
  }
}

watch(() => route.params.code, (newCode) => {
  if (newCode) {
    const code = Array.isArray(route.params.code) ? route.params.code[0] : route.params.code
    workshopCode.value = code
    initializeDates()
    loadData(code)
  }
}, { immediate: true })

onBeforeMount(() => {
  // 设置自动刷新
  refreshInterval.value = setInterval(() => {
    checkAndUpdateDates()
    if (workshopCode.value)
      loadData(workshopCode.value)
  }, 300000) // 5分钟刷新一次
})

onBeforeUnmount(() => {
  // 清除自动刷新
  if (refreshInterval.value) {
    clearInterval(refreshInterval.value)
  }
})

// 重置时间
function resetDates() {
  initializeDates()
}
</script>

<template>
  <div class="relative box-border h-full max-w-full w-full overflow-hidden light:bg-slate-100 dark:from-slate-900 dark:to-slate-950 dark:bg-gradient-to-br" :class="{ light: !isDark, dark: isDark }">
    <!-- 背景装饰 -->
    <div class="pointer-events-none absolute inset-0 z-0">
      <div class="bg-grid" />
      <div class="bg-glow" />
    </div>

    <!-- 浮动Logo -->
    <img v-if="show" ref="el" :src="Honghong" alt="Logo" class="floating-logo" :style="style">

    <!-- 控制面板 -->
    <div v-show="aggregationStore.isSearchVisible" class="relative z-5 flex justify-end p-4">
      <div class="control-group">
        <DatePicker
          v-model="startTime"
          fluid
          show-icon
          date-format="yy-mm-dd"
          v-bind="{ showTime: true, hourFormat: '24' }"
          class="min-w-32"
        >
          <template #inputicon="slotProps">
            <i class="pi pi-clock" @click="slotProps.clickCallback" />
          </template>
        </DatePicker>
        <DatePicker
          v-model="endTime"
          fluid
          show-icon
          date-format="yy-mm-dd"
          v-bind="{ showTime: true, hourFormat: '24' }"
          class="min-w-32"
        >
          <template #inputicon="slotProps">
            <i class="pi pi-clock" @click="slotProps.clickCallback" />
          </template>
        </DatePicker>
        <Button
          size="small"
          icon="pi pi-refresh"
          class="rounded-lg transition-all duration-300 ease-in-out hover:rotate-180"
          severity="secondary"
          @click="resetDates"
        />
      </div>
    </div>

    <!-- 数据展示区域 -->
    <div class="dashboard-content relative z-5 box-border h-[calc(100vh-4rem)] max-w-full w-full overflow-x-hidden overflow-y-auto px-2 pb-2">
      <div class="box-border max-w-full w-full flex flex-col gap-2">
        <LineContainer
          v-for="item in lineList"
          :key="item.code"
          :code="item.code"
          :start-time="startTime!"
          :end-time="endTime!"
        />
      </div>
    </div>
  </div>
</template>

<style scoped>
.bg-grid {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image:
    linear-gradient(rgba(59, 130, 246, 0.1) 1px, transparent 1px),
    linear-gradient(90deg, rgba(59, 130, 246, 0.1) 1px, transparent 1px);
  background-size: 50px 50px;
}

.light .bg-grid {
  background-image:
    linear-gradient(rgba(59, 130, 246, 0.05) 1px, transparent 1px),
    linear-gradient(90deg, rgba(59, 130, 246, 0.05) 1px, transparent 1px);
}

.bg-glow {
  position: absolute;
  top: 20%;
  left: 20%;
  width: 60%;
  height: 60%;
  background: radial-gradient(ellipse at center, rgba(59, 130, 246, 0.07) 0%, transparent 70%);
}

.light .bg-glow {
  background: radial-gradient(ellipse at center, rgba(59, 130, 246, 0.04) 0%, transparent 70%);
}

/* 浮动Logo */
.floating-logo {
  position: fixed;
  z-index: 999;
  width: 8rem;
  cursor: grab;
  filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.3));
  transition: transform 0.3s ease;
}

.floating-logo:hover {
  transform: scale(1.05);
}

.control-group {
  display: flex;
  gap: 0.75rem;
  align-items: center;
  background: var(--bg-container);
  backdrop-filter: blur(12px);
  border: 1px solid rgba(59, 130, 246, 0.25);
  border-radius: 10px;
  padding: 0.75rem;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.25);
}

.light .control-group {
  background: var(--bg-container);
  border-color: rgba(59, 130, 246, 0.2);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

/* 自定义滚动条 */
.dashboard-content::-webkit-scrollbar {
  width: 8px;
}

.dashboard-content::-webkit-scrollbar-track {
  background: rgba(30, 41, 59, 0.2);
  border-radius: 4px;
}

.dashboard-content::-webkit-scrollbar-thumb {
  background: linear-gradient(180deg, rgba(59, 130, 246, 0.5) 0%, rgba(147, 51, 234, 0.5) 100%);
  border-radius: 4px;
  transition: background 0.3s ease;
}

.dashboard-content::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(180deg, rgba(59, 130, 246, 0.7) 0%, rgba(147, 51, 234, 0.7) 100%);
}

.light .dashboard-content::-webkit-scrollbar-track {
  background: rgba(226, 232, 240, 0.8);
}

.light .dashboard-content::-webkit-scrollbar-thumb {
  background: linear-gradient(180deg, rgba(59, 130, 246, 0.3) 0%, rgba(147, 51, 234, 0.3) 100%);
}

.light .dashboard-content::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(180deg, rgba(59, 130, 246, 0.5) 0%, rgba(147, 51, 234, 0.5) 100%);
}
</style>
