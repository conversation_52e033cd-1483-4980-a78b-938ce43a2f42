<script setup lang="ts">
import { toTypedSchema } from '@vee-validate/zod'
import type { DataTablePageEvent } from 'primevue/datatable'
import { z } from '~/utils/zext'
import { theoreticalSampleApi } from '~/api/theoretical-sample'
import type { TheoreticalSample } from '~/api/theoretical-sample/types'
import type { PageData } from '~/api/common/type'
import PageContainer from '~/components/common/PageContainer.vue'
import { handleFileExport } from '~/utils/export'

const loading = ref<boolean>(false)
const data = ref<TheoreticalSample[]>([])
const total = ref(0)

const pageData = reactive<PageData>({
  pageNumber: 0,
  pageSize: 10,
})

// 搜索表单
const searchForm = useForm({
  validationSchema: toTypedSchema(z.object({
    lineCode: z.string().optional(),
    deviceCode: z.string().optional(),
    productModel: z.string().optional(),
    sample: z.boolean().optional(),
  })),
})

// 搜索功能
const search = searchForm.handleSubmit(async (searchParams) => {
  try {
    loading.value = true
    const res = await theoreticalSampleApi.page({ searchParams, pageData })
    data.value = res.list
    total.value = res.total
  }
  finally {
    loading.value = false
  }
})

// 分页处理
function page(e: DataTablePageEvent) {
  pageData.pageNumber = e.page
  pageData.pageSize = e.rows
  search()
}

// 页面加载时执行搜索
onMounted(() => {
  search()
})

// 抽样状态选项
// const sampleOptions = [
//   { label: '是', value: true },
//   { label: '否', value: false },
// ]

// 导出
async function handleExport() {
  await handleFileExport(
    () => theoreticalSampleApi.export(searchForm.values),
    '理论CT缺失数据.xlsx',
    isLoading => loading.value = isLoading,
  )
}
</script>

<template>
  <PageContainer class="mt-4">
    <!-- 搜索表单 -->
    <SearchBox :loading="loading" @submit="search" @search="search">
      <FInput name="lineCode" label="线体编码" />
      <FInput name="deviceCode" label="设备编码" />
      <FInput name="productModel" label="产品型号" />
      <!-- <FSelect name="sample" label="是否抽样" :options="sampleOptions" /> -->
    </SearchBox>

    <ButtonGroup class="pl-8">
      <Button outlined icon="pi pi-download" @click="handleExport()" />
    </ButtonGroup>

    <!-- 数据表格 -->
    <DataTable
      class="p-4"
      :value="data"
      lazy
      paginator
      data-key="lineCode"
      :rows="pageData.pageSize"
      :total-records="total"
      @page="page"
    >
      <Column field="lineCode" header="线体编码" />
      <Column field="productModel" header="产品型号" />
      <Column field="deviceName" header="设备名称" />
      <Column field="deviceCode" header="设备编码" />
      <!-- <Column header="是否抽样">
        <template #body="slotProps">
          <Tag
            :value="slotProps.data.sample ? '是' : '否'"
            :severity="slotProps.data.sample ? 'success' : 'secondary'"
          />
        </template>
      </Column> -->
      <template #empty>
        <TableEmpty />
      </template>
    </DataTable>
  </PageContainer>
</template>
