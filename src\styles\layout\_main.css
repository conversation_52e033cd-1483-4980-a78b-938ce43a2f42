.layout-main-container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  justify-content: space-between;
  padding: 4rem 1rem 0 1rem;
  transition: margin-left var(--layout-section-transition-duration);
}

.layout-main {
  flex: 1 1 auto;
  padding-bottom: 2rem;
}

.dashboard-main-container {
  display: flex;
  flex-direction: column;
  min-height: 100dvh;
  height: 100dvh;
  justify-content: space-between;
  padding: 4rem 0 0 0;
  transition: margin-left var(--layout-section-transition-duration);
}

.dashboard-main {
  height: 100%;
}

.index-main-container {
  display: flex;
  flex-direction: column;
  min-height: 100dvh;
  height: 100dvh;
  justify-content: space-between;
  padding: 4rem 0 0 0;
  transition: margin-left var(--layout-section-transition-duration);
}

.index-main {
  height: 100%;
  width: 100%;
}
