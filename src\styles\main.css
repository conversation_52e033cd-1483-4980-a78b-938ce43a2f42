@layer reset,preflights, primevue,primeicon, icons,utilities;

@import '@unocss/reset/tailwind.css' layer(reset);
@import 'primeicons/primeicons.css' layer(primeicon);
@import './layout/layout.css';

html,
body,
#app {
  font-size: 1rem;
  height: 100vh;
  overflow: overlay;
  margin: 0;
  padding: 0;
}

::-webkit-scrollbar {
  width: 10px;
}

::-webkit-scrollbar-track {
  border-radius: 10px;
  background: var(--p-surface-100);
  border: 1px solid var(--p-surface-100);
}

.dark ::-webkit-scrollbar-track {
  background: var(--p-surface-800);
  border: 1px solid var(--p-surface-700);
}

::-webkit-scrollbar-thumb {
  border-radius: 10px;
  background: var(--p-surface-200);
}

.dark ::-webkit-scrollbar-thumb {
  background: var(--p-surface-700);
}

::-webkit-scrollbar-thumb:hover {
  background: var(--p-surface-300);
}

.dark ::-webkit-scrollbar-thumb:hover {
  background: var(--p-surface-600);
}


:root {
  --line-container-bg: rgba(15, 23, 42, 0.8);
  --line-container-border: rgba(59, 130, 246, 0.25);
  --line-container-shadow: 0 8px 32px rgba(0, 0, 0, 0.25);
  --border-t-color: rgba(255, 255, 255, 0.1);
  --bg-container: rgba(15, 23, 42, 0.8), // 暗色模式背景
  --border-dark: rgba(59, 130, 246, 0.25), // 暗色模式边框
  --border-light: rgba(59, 130, 246, 0.2), // 亮色模式边框
}

.light {
  --line-container-bg: white;
  --line-container-border: #e2e8f0;
  --bg-container: rgba(255, 255, 255, 0.9), // 亮色模式背景
  --line-container-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  --border-t-color: rgba(59, 130, 246, 0.15);
}
